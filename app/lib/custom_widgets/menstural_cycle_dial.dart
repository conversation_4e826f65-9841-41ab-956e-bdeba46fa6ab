import 'dart:math';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

class MenstrualCycleTracker extends StatefulWidget {
  final int cycleDays;
  final int periodDays;
  final int currentDay;
  final int ovulationDays;
  final int ovulationDayStart;
  final double size;
  final TextStyle? textStyle;
  final Color outerCircleColor;
  final Color periodArcColor;
  final Color ovulationArcColor;
  final Color innerCircleColor;
  final Color pointerColor;

  const MenstrualCycleTracker({
    Key? key,
    required this.cycleDays,
    required this.periodDays,
    required this.currentDay,
    required this.ovulationDays,
    required this.ovulationDayStart,
    this.size = 300, // default size if not provided
    this.textStyle, // optional custom text style for numbers
    this.outerCircleColor = Colors.grey,
    this.periodArcColor = Colors.purple,
    this.ovulationArcColor = Colors.yellow, // Color for ovulation arc
    this.innerCircleColor = Colors.purple,
    this.pointerColor = Colors.white,
  }) : super(key: key);

  @override
  State<MenstrualCycleTracker> createState() => _MenstrualCycleTrackerState();
}

class _MenstrualCycleTrackerState extends State<MenstrualCycleTracker> {
  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(widget.size, widget.size),
      painter: CycleTrackerPainter(
        cycleDays: widget.cycleDays,
        periodDays: widget.periodDays,
        currentDay: widget.currentDay,
        ovulationDays: widget.ovulationDays,
        ovulationDayStart: widget.ovulationDayStart,
        textStyle: widget.textStyle ??
            const TextStyle(
                color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
        outerCircleColor: widget.outerCircleColor,
        periodArcColor: widget.periodArcColor,
        ovulationArcColor: widget.ovulationArcColor,
        innerCircleColor: widget.innerCircleColor,
        pointerColor: widget.pointerColor,
      ),
    );
  }
}

class CycleTrackerPainter extends CustomPainter {
  final int cycleDays;
  final int periodDays;
  final int currentDay;
  final int ovulationDays;
  final int ovulationDayStart;
  final TextStyle textStyle;
  final Color outerCircleColor;
  final Color periodArcColor;
  final Color ovulationArcColor;
  final Color innerCircleColor;
  final Color pointerColor;

  CycleTrackerPainter({
    required this.cycleDays,
    required this.periodDays,
    required this.currentDay,
    required this.ovulationDays,
    required this.ovulationDayStart,
    required this.textStyle,
    required this.outerCircleColor,
    required this.periodArcColor,
    required this.ovulationArcColor,
    required this.innerCircleColor,
    required this.pointerColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = min(size.width, size.height) / 2;

    // Draw shadow for the dial (outer circle) - box-shadow: 0px 4px 4px 0px #00000040
    final dialShadowPath = Path()
      ..addOval(
          Rect.fromCircle(center: center.translate(0, 4), radius: radius));
    canvas.drawShadow(dialShadowPath, const Color(0x40000000), 4.0, false);

    // Draw outer circle
    final outerPaint = Paint()
      ..color = outerCircleColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 20;
    canvas.drawCircle(center, radius, outerPaint);

    // Draw curvy period arc
    final periodPaint = Paint()
      ..color = periodArcColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 20
      ..strokeCap = StrokeCap.round;
    final periodAngle = 2 * pi * (periodDays / cycleDays);
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      -pi / 2,
      periodAngle,
      false,
      periodPaint,
    );

    // Draw ovulation arc
    final ovulationPaint = Paint()
      ..color = ovulationArcColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 20
      ..strokeCap = StrokeCap.round;
    final ovulationStartAngle =
        2 * pi * ((ovulationDayStart - 1) / cycleDays) - pi / 2;
    final ovulationAngle = 2 * pi * (ovulationDays / cycleDays);
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      ovulationStartAngle,
      ovulationAngle,
      false,
      ovulationPaint,
    );

    // Draw day numbers on the arc
    final textPainter = TextPainter(textDirection: ui.TextDirection.ltr);

    for (int i = 1; i <= periodDays; i++) {
      final angle = 2 * pi * ((i - 0.5) / cycleDays) - pi / 2;
      final textCenter = Offset(
        center.dx + radius * cos(angle),
        center.dy + radius * sin(angle),
      );

      textPainter.text = TextSpan(text: i.toString(), style: textStyle);
      textPainter.layout();
      textPainter.paint(
        canvas,
        textCenter - Offset(textPainter.width / 2, textPainter.height / 2),
      );
    }

    // Draw circular container (inner circle) with shadow
    final containerPaint2 = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final innerRadius2 = radius - 10;

    // Create a path for the inner circle
    final innerCirclePath2 = Path()
      ..addOval(Rect.fromCircle(center: center, radius: innerRadius2));

    // Draw the inner circle
    canvas.drawCircle(center, innerRadius2, containerPaint2);

    // Draw circular container (inner circle) with shadow
    final containerPaint = Paint()
      ..color = innerCircleColor
      ..style = PaintingStyle.fill;

    final innerRadius = radius - 27;

    // Create a path for the inner circle
    final innerCirclePath = Path()
      ..addOval(Rect.fromCircle(center: center, radius: innerRadius));

    // Draw shadow for the inner circle (make it more prominent)
    canvas.drawShadow(
        innerCirclePath, Colors.black.withOpacity(0.4), 16.0, true);

    // Draw the inner circle
    canvas.drawCircle(center, innerRadius, containerPaint);

    // Draw the current day indicator text
// Draw the current day indicator text
// Draw the current day indicator text
    final currentDayText = TextPainter(
      text: TextSpan(
        text:
            '${currentDay <= periodDays ? 'Day $currentDay\nof your period' : currentDay >= 14 && currentDay <= 14 + 7 ? 'Day ${currentDay - 13}\nof ovulation' : 'Day $currentDay\nof your cycle'}',
        style: textStyle.copyWith(fontSize: 18, fontWeight: FontWeight.bold),
      ),
      textAlign: TextAlign.center, // Center align the text
      textDirection: TextDirection.ltr, // Specify the text direction
    );
    currentDayText.layout();
    currentDayText.paint(
      canvas,
      Offset(center.dx - currentDayText.width / 2,
          center.dy - currentDayText.height / 2),
    );

    // Draw triangle pointer (extending outside the inner circle) with shadow
    final pointerPaint = Paint()
      ..color = pointerColor
      ..style = PaintingStyle.fill;

    final pointerRadius =
        innerRadius + 15; // Extend the pointer outside the inner circle
    final currentDayAngle = 2 * pi * ((currentDay - 0.5) / cycleDays) - pi / 2;

    // Create triangle path for the pointer
    final trianglePath = Path()
      ..moveTo(
        center.dx + pointerRadius * cos(currentDayAngle), // Tip of the triangle
        center.dy + pointerRadius * sin(currentDayAngle),
      )
      ..lineTo(
        center.dx +
            (pointerRadius - 20) *
                cos(currentDayAngle - 0.1), // Bottom left of the triangle
        center.dy + (pointerRadius - 15) * sin(currentDayAngle - 0.1),
      )
      ..lineTo(
        center.dx +
            (pointerRadius - 15) *
                cos(currentDayAngle + 0.1), // Bottom right of the triangle
        center.dy + (pointerRadius - 20) * sin(currentDayAngle + 0.1),
      )
      ..close();
    // Create triangle path for the pointer
    final trianglePathShadow = Path()
      ..moveTo(
        center.dx + pointerRadius * cos(currentDayAngle), // Tip of the triangle
        center.dy + pointerRadius * sin(currentDayAngle),
      )
      ..lineTo(
        center.dx +
            (pointerRadius - 20) *
                cos(currentDayAngle + 0.1), // Bottom right of the triangle
        center.dy + (pointerRadius - 20) * sin(currentDayAngle + 0.1),
      )
      ..close();

    // Draw shadow for the pointer (make it more prominent)
    canvas.drawShadow(
        trianglePathShadow, Colors.black.withOpacity(0.5), 12.0, true);

    // Draw the pointer
    canvas.drawPath(trianglePath, pointerPaint);
  }

  @override
  bool shouldRepaint(covariant CycleTrackerPainter oldDelegate) {
    return oldDelegate.cycleDays != cycleDays ||
        oldDelegate.periodDays != periodDays ||
        oldDelegate.currentDay != currentDay ||
        oldDelegate.ovulationDays != ovulationDays ||
        oldDelegate.ovulationDayStart != ovulationDayStart ||
        oldDelegate.outerCircleColor != outerCircleColor ||
        oldDelegate.periodArcColor != periodArcColor ||
        oldDelegate.ovulationArcColor != ovulationArcColor ||
        oldDelegate.innerCircleColor != innerCircleColor ||
        oldDelegate.pointerColor != pointerColor;
  }
}
